/**
 * الملف الرئيسي للـ JavaScript
 * Main JavaScript file for the platform
 */

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * تهيئة التطبيق
 */
function initializeApp() {
    console.log('تم تحميل منصة الكورسات التعليمية');
    
    // تهيئة المكونات
    initializeNavigation();
    initializeAnimations();
    initializeTooltips();
    initializeAlerts();
    
    // فحص حالة التطبيق
    checkAppHealth();
}

/**
 * تهيئة شريط التنقل
 */
function initializeNavigation() {
    const navbar = document.querySelector('.navbar');
    
    // إضافة تأثير الشفافية عند التمرير
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            navbar.classList.add('navbar-scrolled');
        } else {
            navbar.classList.remove('navbar-scrolled');
        }
    });
    
    // تفعيل الرابط النشط
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    
    navLinks.forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
}

/**
 * تهيئة الحركات والتأثيرات
 */
function initializeAnimations() {
    // تأثير الظهور التدريجي للعناصر
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-up');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // مراقبة العناصر القابلة للحركة
    const animatedElements = document.querySelectorAll('.feature-card, .specialization-card, .section-title');
    animatedElements.forEach(el => observer.observe(el));
}

/**
 * تهيئة التلميحات
 */
function initializeTooltips() {
    // تفعيل Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * تهيئة التنبيهات
 */
function initializeAlerts() {
    // إخفاء التنبيهات تلقائياً بعد 5 ثوان
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
}

/**
 * فحص حالة التطبيق
 */
async function checkAppHealth() {
    try {
        const response = await fetch('/api/health');
        const data = await response.json();
        
        if (data.status === 'healthy') {
            console.log('✅ التطبيق يعمل بشكل طبيعي');
        }
    } catch (error) {
        console.error('❌ خطأ في الاتصال بالخادم:', error);
    }
}

/**
 * عرض رسالة تحميل
 */
function showLoading(element) {
    if (element) {
        element.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحميل...';
        element.disabled = true;
    }
}

/**
 * إخفاء رسالة التحميل
 */
function hideLoading(element, originalText) {
    if (element) {
        element.innerHTML = originalText;
        element.disabled = false;
    }
}

/**
 * عرض رسالة نجاح
 */
function showSuccess(message) {
    showAlert(message, 'success');
}

/**
 * عرض رسالة خطأ
 */
function showError(message) {
    showAlert(message, 'danger');
}

/**
 * عرض تنبيه
 */
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alert-container') || createAlertContainer();
    
    const alertElement = document.createElement('div');
    alertElement.className = `alert alert-${type} alert-dismissible fade show`;
    alertElement.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.appendChild(alertElement);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        const bsAlert = new bootstrap.Alert(alertElement);
        bsAlert.close();
    }, 5000);
}

/**
 * إنشاء حاوية التنبيهات
 */
function createAlertContainer() {
    const container = document.createElement('div');
    container.id = 'alert-container';
    container.className = 'position-fixed top-0 end-0 p-3';
    container.style.zIndex = '9999';
    document.body.appendChild(container);
    return container;
}

/**
 * تنسيق التاريخ
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

/**
 * تنسيق الوقت
 */
function formatTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit'
    });
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * التحقق من قوة كلمة المرور
 */
function validatePassword(password) {
    return {
        length: password.length >= 8,
        uppercase: /[A-Z]/.test(password),
        lowercase: /[a-z]/.test(password),
        number: /\d/.test(password),
        special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    };
}

/**
 * نسخ النص إلى الحافظة
 */
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showSuccess('تم نسخ النص بنجاح');
    } catch (error) {
        console.error('فشل في نسخ النص:', error);
        showError('فشل في نسخ النص');
    }
}

/**
 * تحديث عداد الوقت
 */
function updateTimeAgo(element, timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInSeconds = Math.floor((now - time) / 1000);
    
    let timeAgo;
    if (diffInSeconds < 60) {
        timeAgo = 'منذ لحظات';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        timeAgo = `منذ ${minutes} دقيقة`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        timeAgo = `منذ ${hours} ساعة`;
    } else {
        const days = Math.floor(diffInSeconds / 86400);
        timeAgo = `منذ ${days} يوم`;
    }
    
    element.textContent = timeAgo;
}

// إضافة CSS للحركات
const animationCSS = `
.navbar-scrolled {
    background-color: rgba(13, 110, 253, 0.95) !important;
    backdrop-filter: blur(10px);
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
`;

// إضافة CSS إلى الصفحة
const style = document.createElement('style');
style.textContent = animationCSS;
document.head.appendChild(style);
