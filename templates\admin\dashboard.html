{% extends "base.html" %}

{% block title %}لوحة تحكم الأدمن - {{ platform_name }}{% endblock %}

{% block extra_css %}
<style>
.admin-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    border-radius: 15px;
}

.admin-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    margin-bottom: 1.5rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.quick-action-btn {
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    border: none;
    color: white;
    text-decoration: none;
    display: block;
    text-align: center;
}

.quick-action-btn:hover {
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.specialization-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.specialization-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
    transition: all 0.3s ease;
}

.specialization-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.specialization-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #667eea;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-0">
                        <i class="fas fa-tachometer-alt text-primary me-3"></i>
                        لوحة تحكم الأدمن
                    </h1>
                    <p class="text-muted mt-2">مرحباً {{ current_user.first_name }}، إليك نظرة عامة على المنصة</p>
                </div>
                <div>
                    <span class="badge bg-success fs-6">
                        <i class="fas fa-circle me-1"></i>متصل
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card bg-primary">
                <div class="stat-number" id="total-users">0</div>
                <div class="stat-label">إجمالي المستخدمين</div>
                <i class="fas fa-users fa-2x opacity-50"></i>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card bg-success">
                <div class="stat-number" id="total-instructors">0</div>
                <div class="stat-label">المدرسين</div>
                <i class="fas fa-chalkboard-teacher fa-2x opacity-50"></i>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card bg-info">
                <div class="stat-number" id="total-students">0</div>
                <div class="stat-label">الطلاب</div>
                <i class="fas fa-user-graduate fa-2x opacity-50"></i>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card bg-warning">
                <div class="stat-number" id="total-courses">0</div>
                <div class="stat-label">الكورسات</div>
                <i class="fas fa-book fa-2x opacity-50"></i>
            </div>
        </div>
    </div>

    <!-- الإجراءات السريعة -->
    <div class="row mb-4">
        <div class="col-12">
            <h3 class="mb-3">
                <i class="fas fa-bolt text-warning me-2"></i>
                الإجراءات السريعة
            </h3>
        </div>
        <div class="col-md-3">
            <a href="{{ url_for('admin_specializations') }}" class="quick-action-btn bg-primary">
                <i class="fas fa-stethoscope fa-2x mb-2"></i>
                <div>إدارة التخصصات</div>
                <small>إضافة وتعديل التخصصات</small>
            </a>
        </div>
        <div class="col-md-3">
            <a href="{{ url_for('admin_instructors') }}" class="quick-action-btn bg-success">
                <i class="fas fa-chalkboard-teacher fa-2x mb-2"></i>
                <div>إدارة المدرسين</div>
                <small>عرض وإدارة المدرسين</small>
            </a>
        </div>
        <div class="col-md-3">
            <a href="{{ url_for('admin_students') }}" class="quick-action-btn bg-info">
                <i class="fas fa-user-graduate fa-2x mb-2"></i>
                <div>إدارة الطلاب</div>
                <small>عرض وإدارة الطلاب</small>
            </a>
        </div>
        <div class="col-md-3">
            <a href="{{ url_for('admin_analytics') }}" class="quick-action-btn bg-warning">
                <i class="fas fa-chart-line fa-2x mb-2"></i>
                <div>التحليلات</div>
                <small>إحصائيات مفصلة</small>
            </a>
        </div>
    </div>

    <!-- التخصصات الحالية -->
    <div class="row">
        <div class="col-12">
            <div class="card admin-card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-stethoscope me-2"></i>
                        التخصصات المتاحة
                    </h4>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <p class="text-muted mb-0">إدارة التخصصات المتاحة في المنصة</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSpecializationModal">
                            <i class="fas fa-plus me-2"></i>إضافة تخصص جديد
                        </button>
                    </div>
                    
                    <div id="specializations-container">
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                            <p class="mt-2 text-muted">جاري تحميل التخصصات...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة تخصص جديد -->
<div class="modal fade" id="addSpecializationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>إضافة تخصص جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addSpecializationForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="spec_name" class="form-label">اسم التخصص (عربي)</label>
                                <input type="text" class="form-control" id="spec_name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="spec_name_en" class="form-label">اسم التخصص (إنجليزي)</label>
                                <input type="text" class="form-control" id="spec_name_en" name="name_en" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="spec_description" class="form-label">وصف التخصص</label>
                        <textarea class="form-control" id="spec_description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="spec_icon" class="form-label">أيقونة التخصص</label>
                                <select class="form-select" id="spec_icon" name="icon" required>
                                    <option value="">اختر أيقونة</option>
                                    <option value="fas fa-microscope">🔬 المجهر (التحليل الطبي)</option>
                                    <option value="fas fa-x-ray">📡 الأشعة السينية</option>
                                    <option value="fas fa-syringe">💉 الحقنة (التخدير)</option>
                                    <option value="fas fa-stethoscope">🩺 السماعة الطبية</option>
                                    <option value="fas fa-heartbeat">💓 نبضات القلب</option>
                                    <option value="fas fa-user-md">👨‍⚕️ الطبيب</option>
                                    <option value="fas fa-hospital">🏥 المستشفى</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">معاينة الأيقونة</label>
                                <div class="border rounded p-3 text-center">
                                    <i id="icon-preview" class="fas fa-question fa-3x text-muted"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">المراحل المتاحة</label>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="stage2" name="stages" value="2" checked>
                                    <label class="form-check-label" for="stage2">المرحلة الثانية</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="stage3" name="stages" value="3" checked>
                                    <label class="form-check-label" for="stage3">المرحلة الثالثة</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="stage4" name="stages" value="4" checked>
                                    <label class="form-check-label" for="stage4">المرحلة الرابعة</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="saveSpecializationBtn">
                    <i class="fas fa-save me-2"></i>حفظ التخصص
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/admin-dashboard.js') }}"></script>
{% endblock %}
