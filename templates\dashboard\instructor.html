{% extends "base.html" %}

{% block title %}لوحة تحكم المدرس - {{ platform_name }}{% endblock %}

{% block extra_css %}
<style>
    .dashboard-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border: 1px solid #e9ecef;
        height: 100%;
    }
    
    .dashboard-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
        transition: transform 0.3s ease;
    }
    
    .stats-card:hover {
        transform: translateY(-3px);
    }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    
    .student-card {
        background: white;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }
    
    .student-card:hover {
        border-color: #007bff;
        box-shadow: 0 2px 8px rgba(0,123,255,0.15);
    }
    
    .student-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 16px;
        font-weight: bold;
    }
    
    .course-count {
        background: #e3f2fd;
        color: #1976d2;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .welcome-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .quick-action-btn {
        background: rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.3);
        color: white;
        border-radius: 8px;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
    }
    
    .quick-action-btn:hover {
        background: rgba(255,255,255,0.3);
        color: white;
        transform: translateY(-2px);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- ترحيب -->
    <div class="welcome-section">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="h3 mb-2">
                    <i class="fas fa-chalkboard-teacher me-2"></i>
                    مرحباً {{ current_user.full_name }}
                </h1>
                <p class="mb-0 opacity-75">
                    لوحة تحكم المدرس - إدارة الطلاب والكورسات
                </p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <button class="btn quick-action-btn" onclick="refreshData()">
                        <i class="fas fa-sync-alt me-2"></i>
                        تحديث البيانات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4" id="statisticsSection">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number" id="totalStudents">0</div>
                <div>طلابي</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                <div class="stats-number" id="activeCourses">0</div>
                <div>الكورسات النشطة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);">
                <div class="stats-number" id="totalEnrollments">0</div>
                <div>إجمالي التسجيلات</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
                <div class="stats-number" id="activeStudents">0</div>
                <div>طلاب نشطين</div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- قائمة الطلاب -->
        <div class="col-lg-8">
            <div class="dashboard-card p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="mb-0">
                        <i class="fas fa-user-graduate text-primary me-2"></i>
                        طلابي
                    </h5>
                    <div class="d-flex gap-2">
                        <div class="input-group" style="width: 250px;">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control" id="searchInput" 
                                   placeholder="البحث في الطلاب...">
                        </div>
                    </div>
                </div>

                <!-- قائمة الطلاب -->
                <div id="studentsContainer">
                    <!-- سيتم تحميل الطلاب هنا -->
                </div>

                <!-- Loading Spinner -->
                <div class="text-center py-4" id="loadingSpinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2 text-muted">جاري تحميل بيانات الطلاب...</p>
                </div>

                <!-- رسالة عدم وجود بيانات -->
                <div class="text-center py-4 d-none" id="noStudentsMessage">
                    <i class="fas fa-user-graduate fa-3x text-muted mb-3"></i>
                    <h6 class="text-muted">لا يوجد طلاب مرتبطين بك</h6>
                    <p class="text-muted mb-0">لم يتم ربط أي طلاب بحسابك بعد</p>
                </div>
            </div>
        </div>

        <!-- الشريط الجانبي -->
        <div class="col-lg-4">
            <!-- إجراءات سريعة -->
            <div class="dashboard-card p-4 mb-4">
                <h6 class="mb-3">
                    <i class="fas fa-bolt text-warning me-2"></i>
                    إجراءات سريعة
                </h6>
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" onclick="viewAllCourses()">
                        <i class="fas fa-book me-2"></i>
                        عرض جميع الكورسات
                    </button>
                    <button class="btn btn-outline-success" onclick="createNewCourse()">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء كورس جديد
                    </button>
                    <button class="btn btn-outline-info" onclick="viewReports()">
                        <i class="fas fa-chart-bar me-2"></i>
                        التقارير والإحصائيات
                    </button>
                </div>
            </div>

            <!-- آخر النشاطات -->
            <div class="dashboard-card p-4">
                <h6 class="mb-3">
                    <i class="fas fa-clock text-info me-2"></i>
                    آخر النشاطات
                </h6>
                <div id="recentActivities">
                    <div class="text-center py-3">
                        <small class="text-muted">لا توجد نشاطات حديثة</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
/**
 * لوحة تحكم المدرس
 */

let studentsData = [];

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadInstructorData();
    setupEventListeners();
});

/**
 * تحميل بيانات المدرس
 */
async function loadInstructorData() {
    showLoading(true);
    
    try {
        const response = await fetchWithAuth('/api/instructor/students');

        if (response.success) {
            studentsData = response.students;
            updateStatistics();
            renderStudents();
        } else {
            showError('فشل في تحميل بيانات الطلاب');
        }
        
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        showError('حدث خطأ في تحميل البيانات');
    } finally {
        showLoading(false);
    }
}

/**
 * إعداد مستمعي الأحداث
 */
function setupEventListeners() {
    const searchInput = document.getElementById('searchInput');
    searchInput.addEventListener('input', debounce(handleSearch, 300));
}

/**
 * معالجة البحث
 */
function handleSearch(event) {
    const searchTerm = event.target.value.trim().toLowerCase();
    
    if (!searchTerm) {
        renderStudents();
        return;
    }

    const filteredStudents = studentsData.filter(student => 
        student.full_name?.toLowerCase().includes(searchTerm) ||
        student.email?.toLowerCase().includes(searchTerm) ||
        student.telegram_id?.toLowerCase().includes(searchTerm)
    );

    renderStudents(filteredStudents);
}

/**
 * عرض الطلاب
 */
function renderStudents(students = studentsData) {
    const container = document.getElementById('studentsContainer');
    const noDataMessage = document.getElementById('noStudentsMessage');

    if (students.length === 0) {
        container.innerHTML = '';
        noDataMessage.classList.remove('d-none');
        return;
    }

    noDataMessage.classList.add('d-none');

    const studentsHTML = students.map(student => createStudentCard(student)).join('');
    container.innerHTML = studentsHTML;
}

/**
 * إنشاء بطاقة طالب
 */
function createStudentCard(student) {
    const avatar = student.full_name ? student.full_name.charAt(0).toUpperCase() : 'ط';
    const statusClass = student.active ? 'text-success' : 'text-danger';
    const statusIcon = student.active ? 'fa-check-circle' : 'fa-times-circle';
    const statusText = student.active ? 'نشط' : 'غير نشط';

    return `
        <div class="student-card p-3 mb-3">
            <div class="d-flex align-items-center">
                <div class="student-avatar me-3">
                    ${avatar}
                </div>
                <div class="flex-grow-1">
                    <h6 class="mb-1">${student.full_name || 'غير محدد'}</h6>
                    <p class="text-muted mb-1 small">
                        <i class="fas fa-envelope me-1"></i>
                        ${student.email || 'غير محدد'}
                    </p>
                    <p class="text-muted mb-0 small">
                        <i class="fas fa-graduation-cap me-1"></i>
                        ${student.specialization_name || 'غير محدد'}
                    </p>
                </div>
                <div class="text-end">
                    <div class="mb-2">
                        <span class="course-count">
                            <i class="fas fa-book me-1"></i>
                            ${student.enrollments_count || 0} كورس
                        </span>
                    </div>
                    <small class="${statusClass}">
                        <i class="fas ${statusIcon} me-1"></i>
                        ${statusText}
                    </small>
                </div>
            </div>
        </div>
    `;
}

/**
 * تحديث الإحصائيات
 */
function updateStatistics() {
    const totalStudents = studentsData.length;
    const activeStudents = studentsData.filter(s => s.active).length;
    const totalEnrollments = studentsData.reduce((sum, s) => sum + (s.enrollments_count || 0), 0);
    const activeCourses = studentsData.reduce((sum, s) => sum + (s.active_courses || 0), 0);

    document.getElementById('totalStudents').textContent = totalStudents;
    document.getElementById('activeStudents').textContent = activeStudents;
    document.getElementById('totalEnrollments').textContent = totalEnrollments;
    document.getElementById('activeCourses').textContent = activeCourses;
}

/**
 * تحديث البيانات
 */
async function refreshData() {
    await loadInstructorData();
}

/**
 * عرض/إخفاء مؤشر التحميل
 */
function showLoading(show) {
    const spinner = document.getElementById('loadingSpinner');
    const container = document.getElementById('studentsContainer');
    
    if (show) {
        spinner.classList.remove('d-none');
        container.innerHTML = '';
    } else {
        spinner.classList.add('d-none');
    }
}

// إجراءات سريعة
function viewAllCourses() {
    window.location.href = '/courses';
}

function createNewCourse() {
    // سيتم تنفيذها لاحقاً
    showInfo('ميزة إنشاء الكورسات ستكون متاحة قريباً');
}

function viewReports() {
    // سيتم تنفيذها لاحقاً
    showInfo('ميزة التقارير ستكون متاحة قريباً');
}

// دوال مساعدة
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
{% endblock %}
