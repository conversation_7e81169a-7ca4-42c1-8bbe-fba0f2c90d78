"""
أدوات إنشاء المستخدمين
User Creation Utilities

هذه الوحدة مسؤولة عن:
- إنشاء المستخدمين الجدد بكلمات مرور غير مشفرة
- إدارة عملية إنشاء الحسابات
- التحقق من صحة البيانات
- إرسال بيانات الحساب عبر التليجرام
"""

import logging
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timezone
from utils.firebase_utils import get_firebase_manager
from utils.auth_utils import get_auth_manager
from models.database_models import DatabaseModels, UserRole

# إعداد نظام التسجيل
logger = logging.getLogger(__name__)

class UserCreationManager:
    """مدير إنشاء المستخدمين"""
    
    def __init__(self):
        self.firebase_manager = get_firebase_manager()
        self.auth_manager = get_auth_manager()
    
    def create_instructor_account(self, 
                                first_name: str, 
                                last_name: str, 
                                telegram_id: str,
                                specialization_id: Optional[str] = None,
                                permissions: Optional[Dict[str, Any]] = None) -> Tuple[bool, Optional[Dict[str, Any]], str]:
        """
        إنشاء حساب مدرس جديد
        Create new instructor account
        
        Args:
            first_name: الاسم الأول
            last_name: الاسم الأخير
            telegram_id: معرف التليجرام
            specialization_id: معرف التخصص (اختياري)
            permissions: صلاحيات المدرس (اختياري)
            
        Returns:
            (نجح الإنشاء، بيانات الحساب، رسالة)
        """
        try:
            # التحقق من عدم وجود المستخدم مسبقاً
            existing_user = self.firebase_manager.get_user_by_telegram_id(telegram_id)
            if existing_user:
                return False, None, "يوجد حساب مرتبط بهذا المعرف مسبقاً"
            
            # إنشاء إيميل فريد للمدرس
            email = f"instructor_{telegram_id}@rray.com"
            
            # التحقق من عدم وجود الإيميل
            existing_email = self.firebase_manager.get_user_by_email(email)
            if existing_email:
                return False, None, "الإيميل موجود مسبقاً"
            
            # توليد كلمة مرور عشوائية
            password = self.auth_manager.generate_random_password(12)
            
            # إنشاء نموذج المستخدم
            user_data = DatabaseModels.create_user_model(
                email=email,
                telegram_id=telegram_id,
                role=UserRole.INSTRUCTOR,
                first_name=first_name,
                last_name=last_name,
                specialization_id=specialization_id
            )
            
            # إضافة الصلاحيات إذا تم تحديدها
            if permissions:
                user_data['permissions'] = permissions
            
            # إضافة معلومات إضافية
            user_data['status'] = 'active'
            user_data['account_type'] = 'instructor'
            user_data['created_by'] = 'bot_system'
            
            # إنشاء المستخدم مع كلمة مرور غير مشفرة
            user_id = self.auth_manager.create_user_with_plain_password(user_data, password)
            
            if user_id:
                account_info = {
                    'user_id': user_id,
                    'email': email,
                    'password': password,
                    'first_name': first_name,
                    'last_name': last_name,
                    'role': 'instructor',
                    'telegram_id': telegram_id,
                    'specialization_id': specialization_id
                }
                
                logger.info(f"تم إنشاء حساب مدرس جديد: {email}")
                return True, account_info, "تم إنشاء حساب المدرس بنجاح"
            else:
                return False, None, "فشل في إنشاء الحساب"
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء حساب المدرس: {e}")
            return False, None, "حدث خطأ في النظام"
    
    def create_student_account(self, 
                             telegram_id: str,
                             specialization_id: str,
                             instructor_id: Optional[str] = None) -> Tuple[bool, Optional[Dict[str, Any]], str]:
        """
        إنشاء حساب طالب جديد
        Create new student account
        
        Args:
            telegram_id: معرف التليجرام
            specialization_id: معرف التخصص
            instructor_id: معرف المدرس المسؤول (اختياري)
            
        Returns:
            (نجح الإنشاء، بيانات الحساب، رسالة)
        """
        try:
            # التحقق من عدم وجود المستخدم مسبقاً
            existing_user = self.firebase_manager.get_user_by_telegram_id(telegram_id)
            if existing_user:
                return False, None, "يوجد حساب مرتبط بهذا المعرف مسبقاً"
            
            # إنشاء إيميل فريد للطالب
            email = f"student_{telegram_id}@rray.com"
            
            # التحقق من عدم وجود الإيميل
            existing_email = self.firebase_manager.get_user_by_email(email)
            if existing_email:
                return False, None, "الإيميل موجود مسبقاً"
            
            # توليد كلمة مرور عشوائية
            password = self.auth_manager.generate_random_password(10)
            
            # إنشاء نموذج المستخدم
            user_data = DatabaseModels.create_user_model(
                email=email,
                telegram_id=telegram_id,
                role=UserRole.STUDENT,
                first_name="طالب",  # سيتم تحديثه لاحقاً
                last_name=telegram_id,
                specialization_id=specialization_id
            )
            
            # إضافة معلومات إضافية
            user_data['status'] = 'active'
            user_data['account_type'] = 'student'
            user_data['created_by'] = 'invitation_system'
            if instructor_id:
                user_data['invited_by_instructor'] = instructor_id
            
            # إنشاء المستخدم مع كلمة مرور غير مشفرة
            user_id = self.auth_manager.create_user_with_plain_password(user_data, password)
            
            if user_id:
                account_info = {
                    'user_id': user_id,
                    'email': email,
                    'password': password,
                    'first_name': "طالب",
                    'last_name': telegram_id,
                    'role': 'student',
                    'telegram_id': telegram_id,
                    'specialization_id': specialization_id
                }
                
                logger.info(f"تم إنشاء حساب طالب جديد: {email}")
                return True, account_info, "تم إنشاء حساب الطالب بنجاح"
            else:
                return False, None, "فشل في إنشاء الحساب"
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء حساب الطالب: {e}")
            return False, None, "حدث خطأ في النظام"
    
    def update_user_password(self, user_id: str, new_password: str) -> Tuple[bool, str]:
        """
        تحديث كلمة مرور المستخدم (غير مشفرة)
        Update user password (plain text)
        
        Args:
            user_id: معرف المستخدم
            new_password: كلمة المرور الجديدة
            
        Returns:
            (نجح التحديث، رسالة)
        """
        try:
            success = self.firebase_manager.update_user(user_id, {
                'password': new_password,
                'password_encrypted': False,
                'password_updated_at': datetime.now(timezone.utc).isoformat()
            })
            
            if success:
                user = self.firebase_manager.get_user(user_id)
                logger.info(f"تم تحديث كلمة المرور للمستخدم: {user.get('email') if user else user_id}")
                return True, "تم تحديث كلمة المرور بنجاح"
            else:
                return False, "فشل في تحديث كلمة المرور"
            
        except Exception as e:
            logger.error(f"خطأ في تحديث كلمة المرور: {e}")
            return False, "حدث خطأ في النظام"
    
    def validate_user_data(self, user_data: Dict[str, Any]) -> Tuple[bool, str]:
        """
        التحقق من صحة بيانات المستخدم
        Validate user data
        
        Args:
            user_data: بيانات المستخدم
            
        Returns:
            (صحة البيانات، رسالة الخطأ)
        """
        try:
            # التحقق من الحقول المطلوبة
            required_fields = ['first_name', 'telegram_id', 'role']
            for field in required_fields:
                if not user_data.get(field):
                    return False, f"الحقل {field} مطلوب"
            
            # التحقق من صحة معرف التليجرام
            telegram_id = user_data.get('telegram_id')
            if not telegram_id.isdigit() or len(telegram_id) < 5:
                return False, "معرف التليجرام غير صحيح"
            
            # التحقق من صحة الدور
            valid_roles = [UserRole.ADMIN, UserRole.INSTRUCTOR, UserRole.STUDENT]
            if user_data.get('role') not in valid_roles:
                return False, "دور المستخدم غير صحيح"
            
            return True, "البيانات صحيحة"
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من البيانات: {e}")
            return False, "خطأ في التحقق من البيانات"
    
    def get_user_account_info(self, user_id: str) -> Optional[Dict[str, Any]]:
        """
        الحصول على معلومات حساب المستخدم
        Get user account information
        
        Args:
            user_id: معرف المستخدم
            
        Returns:
            معلومات الحساب أو None
        """
        try:
            user = self.firebase_manager.get_user(user_id)
            if not user:
                return None
            
            return {
                'user_id': user_id,
                'email': user.get('email'),
                'password': user.get('password'),  # كلمة المرور غير المشفرة
                'first_name': user.get('first_name'),
                'last_name': user.get('last_name'),
                'role': user.get('role'),
                'telegram_id': user.get('telegram_id'),
                'specialization_id': user.get('specialization_id'),
                'status': user.get('status'),
                'created_at': user.get('created_at'),
                'password_encrypted': user.get('password_encrypted', False)
            }
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات المستخدم: {e}")
            return None

# إنشاء مثيل مشترك
user_creation_manager = UserCreationManager()

def get_user_creation_manager() -> UserCreationManager:
    """الحصول على مثيل User Creation Manager"""
    return user_creation_manager
